import 'dart:convert';

import 'package:catcher_2/catcher_2.dart';
import 'package:catcher_2/model/platform_type.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import './app.dart';

import './common/utils/init_main.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Initialization.initMain();
  // Catcher2Options debugOptions = Catcher2Options(
  //   DialogReportMode(),
  //   [
  //     DiscordHandler(endpoint: 'http://82.112.250.50:5003/log/med'),
  //     ConsoleHandler(),
  //     TelegramReportHandler(botToken: '**********************************************', chatId: '805808683'),
  //   ],
  // );
  // Catcher2Options releaseOptions = Catcher2Options(
  //   DialogReportMode(),
  //   [
  //     Discord<PERSON>andler(endpoint: 'http://82.112.250.50:5003/log/med'),
  //     TelegramReportHandler(botToken: '**********************************************', chatId: '805808683'),
  //   ],
  // );
  //
  // Catcher2(debugConfig: debugOptions, releaseConfig: releaseOptions, rootWidget: );

  runApp(Initialization.initLocalization(const App()));

  // if (Platform.isWindows) {
  //   await windowManager.ensureInitialized();

  //   WindowOptions windowOptions = const WindowOptions(
  //     size: Size(400, 720),
  //     minimumSize: Size(400, 720),
  //     center: true,
  //     backgroundColor: Colors.transparent,
  //     skipTaskbar: false,
  //   );
  //   windowManager.waitUntilReadyToShow(windowOptions, () async {
  //     await windowManager.show();
  //     await windowManager.focus();
  //   });
  // }
}

class DiscordHandler extends ReportHandler {
  final String endpoint;
  final Dio _dio;

  DiscordHandler({required this.endpoint})
      : _dio = Dio(BaseOptions(
      headers: {"Content-Type": "application/json"}
  ));

  Future<bool> handleReport(Report report, BuildContext? context) async {
    final payload = {
      "team": "mobile",
      "level": "ERROR",
      "message": "${report.error}",
      "datetime": DateTime.now().toString(),
      "environment": "stage",
      "context": {
        "service": "app",
        "exception": {
          "stack": "${report.stackTrace}",
          "file": report.screenshot,
          "line": 0,
          "column": 0,
        }
      }
    };
    try {
      final response = await _dio.post(endpoint, data: jsonEncode(payload));
      return response.statusCode == 200;
    } catch (e) {
      debugPrint("Error sending to Discord handler: $e");
      return false;
    }
  }

  @override
  List<PlatformType> getSupportedPlatforms() {
    return [PlatformType.iOS, PlatformType.android];
  }

  @override
  Future<bool> handle(Report report, BuildContext? context) async {
    return handleReport(report, context);
  }
}

class TelegramReportHandler extends ReportHandler {
  final String botToken;
  final String chatId;

  TelegramReportHandler({
    required this.botToken,
    required this.chatId,
  });

  @override
  Future<bool> handle(Report report, BuildContext? context) async {
    final dio = Dio();

    final stack = report.stackTrace?.toString().replaceAll('`', "'") ?? 'No stack trace available.';
    final error = report.error.toString().replaceAll('`', "'");

    final message = '''
🚨 *Crash Report Alert* 🚨

🖥️ *Device Info*
• *Device Parameters:* `${report.deviceParameters}`
• *Platform:* `${report.platformType}`
• *Error Details:* `${report.errorDetails}`

⚡ *Error Message*
`$error`

🛠️ *Stack Trace*
$stack

php
Copy
Edit
    ''';

    final url = 'https://api.telegram.org/bot$botToken/sendMessage';

    try {
      await dio.post(
        url,
        data: {
          'chat_id': chatId,
          'text': message,
          'parse_mode': 'Markdown',
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );
      return true;
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('Failed to send report to Telegram: $e');
      }
      if (kDebugMode) {
        print(stackTrace);
      }
      return false;
    }
  }

  @override
  List<PlatformType> getSupportedPlatforms() {
    return [PlatformType.android, PlatformType.iOS];
  }
}
