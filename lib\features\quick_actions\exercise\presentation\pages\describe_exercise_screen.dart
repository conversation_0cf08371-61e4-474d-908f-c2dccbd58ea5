import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/core/di/injection.dart';
import 'package:cal/features/quick_actions/exercise/data/models/exercise_save_ai_model.dart';
import 'package:cal/features/quick_actions/exercise/presentation/bloc/exercise_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DescribeExerciseScreen extends StatefulWidget {
  const DescribeExerciseScreen({super.key});

  @override
  State<DescribeExerciseScreen> createState() => _DescribeExerciseScreenState();
}

class _DescribeExerciseScreenState extends State<DescribeExerciseScreen> {
  final TextEditingController _descriptionController = TextEditingController();

  @override
  void dispose() {
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ExerciseBloc>(
      create: (context) => getIt<ExerciseBloc>(),
      child: BlocListener<ExerciseBloc, ExerciseState>(
        listener: (context, state) {
          if (state is ExerciseSaved) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Exercise saved successfully!')),
            );
            Navigator.of(context).pop();
          } else if (state is ExerciseError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Error: ${state.message}')),
            );
          }
        },
        child: Scaffold(
          appBar: AppBar(
            title: AppText.titleLarge(
              'Log Exercise(Describe Exercise)',
              color: context.onSecondary,
            ),
            centerTitle: true,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ),
          body: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppText.titleLarge(
                  'وصف التمرين',
                  color: context.onSecondary,
                  fontWeight: FontWeight.bold,
                ),
                const SizedBox(height: 20),
                TextField(
                  controller: _descriptionController,
                  maxLines: 5,
                  decoration: InputDecoration(
                    hintText: 'صف وقت التمرين، وشدته، وما إلى ذلك.',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: context.primaryColor),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: context.primaryColor, width: 2),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                Container(
                  padding: const EdgeInsets.all(16.0),
                  decoration: BoxDecoration(
                    
                    borderRadius: BorderRadius.circular(12.0),
                    border: Border.all(color: context.primaryColor, width: 2.0),
                  ),
                  child: AppText.bodyMedium(
                    'مثال: يوغا ل 60 دقيقة، مع التمدد و الاسترخاء',
                    color: context.onSecondary,
                  ),
                ),
                const Spacer(),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      final exercise = ExerciseSaveAiModel(
                        description: _descriptionController.text,
                      );
                      context.read<ExerciseBloc>().add(SaveExerciseAi(exercise: exercise));
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.primaryColor,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: AppText.titleLarge(
                      'اضافة التمرين',
                      color: context.onPrimaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}


