import 'dart:io';

import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/extentions/size_extension.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/features/home/<USER>/bloc/recent_food_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../generated/assets.dart';
import '../widgets/item_details_bottom_sheet_body.dart';

class ItemDetailsScreen extends StatefulWidget {
  final FoodModel foodModel;

  const ItemDetailsScreen({super.key, required this.foodModel});

  @override
  State<ItemDetailsScreen> createState() => _ItemDetailsScreenState();
}

class _ItemDetailsScreenState extends State<ItemDetailsScreen> {
  double _sheetFraction = 0.545;

  @override
  Widget build(BuildContext context) {
    final foodModel = widget.foodModel;

    return Scaffold(
      backgroundColor: context.background,
      body: SafeArea(
        child: GestureDetector(
          onVerticalDragEnd: (details) {
            if (details.primaryVelocity != null && details.primaryVelocity! > 300) {
              context.pop();
            }
          },
          child: Stack(
            children: [
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 75),
                  height: context.screenHeight * (1 - _sheetFraction),
                  child: foodModel.imagePath != null
                      ? Image.file(
                          File(foodModel.imagePath!),
                          fit: BoxFit.cover,
                        )
                      : const AppImage.asset(
                          Assets.imagesFoodFallback,
                          fit: BoxFit.cover,
                        ),
                ),
              ),

              /// ACTIONS OVER IMAGE
              Padding(
                padding: const EdgeInsetsDirectional.symmetric(horizontal: 26, vertical: 10),
                child: Row(
                  children: [
                    CircleAvatar(
                      backgroundColor: context.onPrimaryColor.withAlpha(127),
                      child: Icon(Icons.more_horiz, color: context.onPrimaryColor),
                    ),
                    const SizedBox(width: 10),
                    CircleAvatar(
                      backgroundColor: context.onPrimaryColor.withAlpha(127),
                      child: Icon(Icons.file_upload_outlined, color: context.onPrimaryColor),
                    ),
                    const Spacer(),
                    GestureDetector(
                      onTap: () {
                        context.pop();
                      },
                      child: CircleAvatar(
                        backgroundColor: context.onPrimaryColor.withAlpha(127),
                        child: Icon(Icons.arrow_forward, color: context.onPrimaryColor),
                      ),
                    ),
                  ],
                ),
              ),

              /// DRAGGABLE SHEET
              NotificationListener<DraggableScrollableNotification>(
                onNotification: (notification) {
                  setState(() {
                    _sheetFraction = notification.extent.clamp(0.545, 0.93);
                  });
                  return true;
                },
                child: DraggableScrollableSheet(
                  initialChildSize: 0.545,
                  minChildSize: 0.545,
                  maxChildSize: 0.93,
                  builder: (_, controller) => Container(
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                      color: context.background,
                    ),
                    child: BlocProvider.value(
                      value: context.read<RecentFoodBloc>(),
                      child: ItemDetailsBottomSheetBody(
                        controller: controller,
                        foodModel: foodModel,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
