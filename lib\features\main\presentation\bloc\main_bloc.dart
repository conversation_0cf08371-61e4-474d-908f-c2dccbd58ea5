import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/local_models/streak_model/streak_model.dart';
import '../../domain/repo/main_repo.dart';

part 'main_event.dart';

part 'main_state.dart';

@injectable
class MainBloc extends Bloc<MainEvent, MainState> {
  final MainRepo mainRepo;

  MainBloc(this.mainRepo) : super(MainState()) {
    on<SaveStreakEvent>(_saveStreak);
    on<GetStreakEvent>(_getStreaks);
  }

  FutureOr<void> _saveStreak(SaveStreakEvent event, Emitter<MainState> emit) async {
    await mainRepo.saveStreak(event.streakModel);
  }

  FutureOr<void> _getStreaks(GetStreakEvent event, Emitter<MainState> emit) async {
    emit(state.copyWith(status: BlocStatus.loading));
    try {
      final res = await mainRepo.getStreaks(event.locale);
      emit(state.copyWith(status: BlocStatus.success, streaks: res));
    } catch (e) {
      emit(state.copyWith(status: BlocStatus.error));
    }
  }
}
