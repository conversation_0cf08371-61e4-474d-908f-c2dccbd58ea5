import 'package:equatable/equatable.dart';

class ExerciseSaveModel extends Equatable {
  final String type;
  final int calories;
  final String intensity;
  final int duration;

  const ExerciseSaveModel({
    required this.type,
    required this.calories,
    required this.intensity,
    required this.duration,
  });

  Map<String, dynamic> toJson() {
    return {
      "type": type,
      "calories": calories,
      "intensity": intensity,
      "duration": duration,
    };
  }

  @override
  List<Object?> get props => [type, calories, intensity, duration];
}


