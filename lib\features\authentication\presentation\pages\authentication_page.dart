import 'dart:io';

import 'package:cal/common/consts/app_keys.dart';
import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/utils/shared_preferences_helper.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/common/widgets/loading_dialog.dart';
import 'package:cal/common/widgets/toast_dialog.dart';
import 'package:cal/features/authentication/presentation/bloc/authentication_event.dart';
import 'package:cal/features/main/presentation/pages/main_screen.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toastification/toastification.dart';
import '../../../../core/di/injection.dart';
import '../bloc/authentication_bloc.dart';

class AuthenticationPage extends StatelessWidget {
  const AuthenticationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<AuthenticationBloc>(
      create: (context) => getIt<AuthenticationBloc>(),
      child: BlocListener<AuthenticationBloc, AuthenticationState>(
        listener: (context, state) {
          if(state.status == AuthenticationStatus.authenticated) {
            LoadingDialog.close();
            ShPH.saveData(key: AppKeys.isAuthenticated, value: true);
            context.pushAndRemoveUntil(const MainScreen());
          }else if(state.status == AuthenticationStatus.error) {
            LoadingDialog.close();
            ToastificationDialog.showToast(msg: 'Error while authorized please try again', context: context, type: ToastificationType.error);
          }else if(state.status == AuthenticationStatus.loading){
            LoadingDialog.show(context);
          }
        },
        child: Scaffold(
          body: SafeArea(
            child: LayoutBuilder(
              builder: (context, constraints) {
                final screenWidth = constraints.maxWidth;
                return SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: ConstrainedBox(
                    constraints: BoxConstraints(minHeight: constraints.maxHeight),
                    child: IntrinsicHeight(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          AppImage.asset(Assets.imagesOrangeAi, size: screenWidth * 0.13),
                          const SizedBox(height: 48),
                          Text(
                            LocaleKeys.auth_complete_account.tr(),
                            style: context.textTheme.headlineMedium!.copyWith(fontWeight: FontWeight.w700),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 24),
                          Text(
                            LocaleKeys.auth_login_to_save_ur_data.tr(),
                            style: context.textTheme.bodyLarge!.copyWith(
                              fontWeight: FontWeight.w400,
                              color: context.onSecondaryContainer.withAlpha(177),
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 40),
                          _AuthButton(
                            icon: Assets.imagesGoogleLogo,
                            label: LocaleKeys.auth_login_with_google.tr(),
                            onTap: () {
                              context.read<AuthenticationBloc>().add(const SignInWithGooglePressed());
                            },
                          ),
                          const SizedBox(height: 14),
                          if (Platform.isIOS)
                            _AuthButton(
                              icon: Assets.imagesAppleLogo,
                              label: LocaleKeys.auth_login_with_apple.tr(),
                              onTap: () {
                                context.read<AuthenticationBloc>().add(const SignInWithApplePressed());
                              },
                            ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

class _AuthButton extends StatelessWidget {
  final String icon;
  final String label;
  final VoidCallback onTap;

  const _AuthButton({
    required this.icon,
    required this.label,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      borderRadius: BorderRadius.circular(100),
      onTap: onTap,
      child: Container(
        width: double.infinity,
        height: 55,
        decoration: BoxDecoration(
          border: Border.all(color: context.primaryColor, width: 1.2),
          borderRadius: BorderRadius.circular(100),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AppImage.asset(icon),
            const SizedBox(width: 12),
            Flexible(
              child: Text(
                label,
                overflow: TextOverflow.ellipsis,
                style: context.textTheme.bodyLarge!.copyWith(
                  fontWeight: FontWeight.w400,
                  color: context.onSecondaryContainer.withAlpha(177),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
