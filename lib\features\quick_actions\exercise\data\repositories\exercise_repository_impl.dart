import 'package:cal/core/network/exceptions.dart';
import 'package:cal/features/quick_actions/exercise/data/datasources/exercise_remote_datasource.dart';
import 'package:cal/features/quick_actions/exercise/data/models/exercise_model.dart';
import 'package:cal/features/quick_actions/exercise/data/models/exercise_save_ai_model.dart';
import 'package:cal/features/quick_actions/exercise/data/models/exercise_save_model.dart';
import 'package:cal/features/quick_actions/exercise/domain/repositories/exercise_repository.dart';
import 'package:dartz/dartz.dart';

class ExerciseRepositoryImpl implements ExerciseRepository {
  final ExerciseRemoteDataSource remoteDataSource;

  ExerciseRepositoryImpl({required this.remoteDataSource});


  @override
  Future<Either<String, ExerciseModel>> saveExercise(ExerciseSaveModel exercise) async {
    try {
      final Either<Failure, ExerciseModel> result = await remoteDataSource.saveExercise(exercise);
      return result.fold(
        (failure) => Left(failure.toString()),
        (model) => Right(model),
      );
    } catch (e) {
      return Left(e.toString());
    }
  }

  @override
  Future<Either<String, ExerciseModel>> saveExerciseAi(ExerciseSaveAiModel exercise) async {
    try {
      final Either<Failure, ExerciseModel> result = await remoteDataSource.saveExerciseAi(exercise);
      return result.fold(
        (failure) => Left(failure.toString()),
        (model) => Right(model),
      );
    } catch (e) {
      return Left(e.toString());
    }
  }
}
