import 'package:cal/common/consts/typedef.dart';
import 'package:cal/features/quick_actions/food_database/domain/repositories/food_database_repository.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class PostMealToLog implements UseCase<void, PostMealToLogParams> {

  final FoodDatabaseRepository foodDatabaseRepository;

  PostMealToLog({required this.foodDatabaseRepository});

  @override
  DataResponse<void> call(PostMealToLogParams params) {
    return foodDatabaseRepository.saveMealToLogRemote(params);
  }
}

class PostMealToLogParams with Params {
  final int foodId;

  @override
  QueryParams getParams() => {
        "food_id": "$foodId",
      };

  PostMealToLogParams({required this.foodId});
}
