import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../common/utils/date_helper.dart';
import '../../../../common/widgets/app_text.dart';
import '../../../main/presentation/bloc/main_bloc.dart';

class DateList extends StatelessWidget {
  final ValueNotifier<DateTime> selectedDateNotifier;

  final double todayCals;

  const DateList({super.key, required this.selectedDateNotifier, required this.todayCals});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 80,
      child: PageView.builder(
        physics: const BouncingScrollPhysics(),
        itemBuilder: (context, i) => Padding(
          padding: const EdgeInsetsDirectional.symmetric(horizontal: 0, vertical: 0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: List.generate(
              DateHelper.getPastFourWeeks(context.locale)[i].length,
              (index) => Expanded(
                child: AppGestureDetector(
                  onTap: () {
                    if (DateHelper.getPastFourWeeks(context.locale)[i][index]['status'] != 'future') {
                      selectedDateNotifier.value = DateTime.parse(DateHelper.getPastFourWeeks(context.locale)[i][index]['fullDate']!);
                    }
                  },
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Stack(
                        alignment: Alignment.topCenter,
                        children: [
                          DateHelper.getPastFourWeeks(context.locale)[i][index]['status']! == 'today'
                              ? TweenAnimationBuilder<double>(
                                  duration: const Duration(milliseconds: 800),
                                  curve: Curves.easeInOut,
                                  tween: Tween<double>(
                                    begin: 0,
                                    end: todayCals,
                                  ),
                                  builder: (context, value, _) {
                                    return SizedBox(
                                      width: 37,
                                      height: 37,
                                      child: CircularProgressIndicator(
                                        value: value,
                                        strokeWidth: 3,
                                        backgroundColor: context.onSecondary.withAlpha(25),
                                        valueColor: const AlwaysStoppedAnimation<Color>(Colors.black),
                                      ),
                                    );
                                  },
                                )
                              : const SizedBox.shrink(),
                          (DateHelper.getPastFourWeeks(context.locale)[i][index]['status']! == 'past')
                              ? BlocBuilder<MainBloc, MainState>(
                                  builder: (context, state) {
                                    if (state.status == BlocStatus.success) {
                                      return state.streaks![index].hasAction
                                          ? Container(
                                              decoration: BoxDecoration(
                                                borderRadius: BorderRadius.circular(100),
                                                border: Border.all(
                                                  color: const Color(0xff7AE157),
                                                  width: 1,
                                                ),
                                              ),
                                              padding: context.locale == const Locale('ar')
                                                  ? const EdgeInsetsDirectional.all(7)
                                                  : const EdgeInsetsDirectional.only(top: 11),
                                              width: 40,
                                              height: 40,
                                              child: AppText.labelSmall(
                                                DateHelper.getPastFourWeeks(context.locale)[i][index][context.locale.languageCode]!,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.black,
                                              ),
                                            )
                                          : DottedBorder(
                                              options: CircularDottedBorderOptions(
                                                dashPattern: [10, 3],
                                                color: Colors.black.withAlpha(127),
                                                strokeWidth: 1,
                                                borderPadding: const EdgeInsets.all(3),
                                              ),
                                              child: Container(
                                                decoration: BoxDecoration(
                                                  borderRadius: BorderRadius.circular(100),
                                                ),
                                                padding: const EdgeInsets.only(top: 4),
                                                width: 40,
                                                height: 40,
                                                child: Center(
                                                  child: AppText.labelSmall(
                                                    DateHelper.getPastFourWeeks(context.locale)[i][index][context.locale.languageCode]!,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black,
                                                  ),
                                                ),
                                              ),
                                            );
                                    } else {
                                      return DottedBorder(
                                        options: CircularDottedBorderOptions(
                                          dashPattern: [10, 3],
                                          color: Colors.black.withAlpha(127),
                                          strokeWidth: 1,
                                          borderPadding: const EdgeInsets.all(3),
                                        ),
                                        child: Container(
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(100),
                                          ),
                                          padding: const EdgeInsets.only(top: 4),
                                          width: 40,
                                          height: 40,
                                          child: Center(
                                            child: AppText.labelSmall(
                                              DateHelper.getPastFourWeeks(context.locale)[i][index][context.locale.languageCode]!,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.black,
                                            ),
                                          ),
                                        ),
                                      );
                                    }
                                  },
                                )
                              : Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(100),
                                  ),
                                  padding: const EdgeInsets.only(top: 3),
                                  width: 40,
                                  height: 40,
                                  child: Center(
                                    child: AppText.labelSmall(
                                      DateHelper.getPastFourWeeks(context.locale)[i][index][context.locale.languageCode]!,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black.withAlpha(75),
                                    ),
                                  ),
                                ),
                        ],
                      ),
                      const SizedBox(height: 6),
                      AppText.labelSmall(
                        DateHelper.getPastFourWeeks(context.locale)[i][index]['date']!,
                        fontWeight: FontWeight.bold,
                        color: DateHelper.getPastFourWeeks(context.locale)[i][index]['status']! == 'future'
                            ? Colors.black.withAlpha(75)
                            : Colors.black,
                      ),
                    ],
                  ),
                ),
              ), 
            ),
          ),
        ),
        reverse: true,
        itemCount: DateHelper.getPastFourWeeks(context.locale).length,
      ),
    );
  }
}
