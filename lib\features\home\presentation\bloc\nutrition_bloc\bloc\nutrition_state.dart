part of 'nutrition_bloc.dart';

class NutritionState extends Equatable {
  final double targetCalories;
  final double targetCarbs;
  final double targetProtein;
  final double targetFat;
  final double consumedCalories;
  final double consumedCarbs;
  final double consumedProtein;
  final double consumedFat;
  final String? error;

  const NutritionState({
    this.targetCalories = 0,
    this.targetCarbs = 0,
    this.targetProtein = 0,
    this.targetFat = 0,
    this.consumedCalories = 0,
    this.consumedCarbs = 0,
    this.consumedProtein = 0,
    this.consumedFat = 0,
    this.error,
  });

  NutritionState copyWith({
    double? targetCalories,
    double? targetCarbs,
    double? targetProtein,
    double? targetFat,
    double? consumedCalories,
    double? consumedCarbs,
    double? consumedProtein,
    double? consumedFat,
    String? error,
  }) {
    return NutritionState(
      targetCalories: targetCalories ?? this.targetCalories,
      targetCarbs: targetCarbs ?? this.targetCarbs,
      targetProtein: targetProtein ?? this.targetProtein,
      targetFat: targetFat ?? this.targetFat,
      consumedCalories: consumedCalories ?? this.consumedCalories,
      consumedCarbs: consumedCarbs ?? this.consumedCarbs,
      consumedProtein: consumedProtein ?? this.consumedProtein,
      consumedFat: consumedFat ?? this.consumedFat,
      error: error,
    );
  }

  @override
  List<Object?> get props => [
        targetCalories,
        targetCarbs,
        targetProtein,
        targetFat,
        consumedCalories,
        consumedCarbs,
        consumedProtein,
        consumedFat,
        error,
      ];
}
