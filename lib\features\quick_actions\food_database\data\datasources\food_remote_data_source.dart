import 'package:cal/core/config/endpoints.dart';
import 'package:cal/features/quick_actions/food_database/data/models/remote_food_database_model.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/post_meal_to_log.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/search_meals_use_case.dart';
import 'package:injectable/injectable.dart';

import '../../../../../core/network/api_handler.dart';
import '../../../../../core/network/http_client.dart';

@lazySingleton
class FoodRemoteDataSource with ApiHandler {
  final HTTPClient dioClient;

  FoodRemoteDataSource({required this.dioClient});

  Future<List<RemoteFoodDatabaseModel>> searchMeals(SearchMealsParams params) async {
    return wrapHandlingApi(tryCall: () => dioClient.get(FoodDatabaseEndPoint.search, queryParameters: params.getParams()), jsonConvert: remoteFoodDatabaseModelFromJson);
  }

  Future<void> postMealToLog(PostMealToLogParams params) async {
    return wrapHandlingApi(tryCall: () => dioClient.post(FoodDatabaseEndPoint.logMeal, params: params.getParams()), jsonConvert: remoteFoodDatabaseModelFromJson);
  }
}
