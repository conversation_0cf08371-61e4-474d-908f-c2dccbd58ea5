import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/features/home/<USER>/widgets/cals_container.dart';
import 'package:cal/features/home/<USER>/widgets/ingredient_card.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../common/widgets/app_image.dart';
import '../../../../core/di/injection.dart';
import '../../../../generated/assets.dart';
import '../../../quick_actions/food_database/data/models/database_food_model.dart';
import '../../../quick_actions/food_database/presentation/bloc/food_database_bloc.dart';
import '../bloc/recent_food_bloc.dart';
import '../pages/edit_value_screen.dart';

class ItemDetailsBottomSheetBody extends StatefulWidget {
  const ItemDetailsBottomSheetBody({
    super.key,
    required this.controller,
    required this.foodModel,
    this.targetCalories = 2000,
    this.targetProtein = 150,
    this.targetCarbs = 250,
    this.targetFat = 65,
  });

  final ScrollController controller;
  final FoodModel foodModel;
  final double targetCalories;
  final double targetProtein;
  final double targetCarbs;
  final double targetFat;

  @override
  State<ItemDetailsBottomSheetBody> createState() => _ItemDetailsBottomSheetBodyState();
}

class _ItemDetailsBottomSheetBodyState extends State<ItemDetailsBottomSheetBody> {
  final List<String> images = [
    Assets.imagesProtien,
    Assets.imagesCarbs,
    Assets.imagesFats,
  ];

  bool isSaved = false;

  // Serving size controllers and state
  late TextEditingController _servingSizeController;
  late FocusNode _servingSizeFocusNode;

  // Store original nutrition values for calculation (per 1 serving)
  late int _originalCalories;
  late double _originalProtein;
  late double _originalCarbs;
  late double _originalFat;

  @override
  void initState() {
    super.initState();

    // Initialize serving size from the food model, default to 1 if not set
    final currentServing = double.tryParse(widget.foodModel.serving ?? '1') ?? 1.0;
    _servingSizeController = TextEditingController(text: _formatServingSize(currentServing));
    _servingSizeFocusNode = FocusNode();

    // Calculate original nutrition values (per 1 serving) from current values
    _calculateOriginalNutritionValues(currentServing);

    _servingSizeController.addListener(_onServingSizeChanged);
    _servingSizeFocusNode.addListener(_onFocusChanged);
  }

  void _calculateOriginalNutritionValues(double currentServing) {
    // If current serving is not 1, calculate the original values per 1 serving
    if (currentServing != 0) {
      _originalCalories = ((widget.foodModel.calories ?? 0) / currentServing).round();
      _originalProtein = (widget.foodModel.protein ?? 0.0) / currentServing;
      _originalCarbs = (widget.foodModel.carbs ?? 0.0) / currentServing;
      _originalFat = (widget.foodModel.fat ?? 0.0) / currentServing;
    } else {
      // Fallback if serving is 0
      _originalCalories = widget.foodModel.calories ?? 0;
      _originalProtein = widget.foodModel.protein ?? 0.0;
      _originalCarbs = widget.foodModel.carbs ?? 0.0;
      _originalFat = widget.foodModel.fat ?? 0.0;
    }
  }

  @override
  void dispose() {
    _servingSizeController.dispose();
    _servingSizeFocusNode.dispose();
    super.dispose();
  }

  void _onServingSizeChanged() {
    final text = _servingSizeController.text;
    if (text.isEmpty) {
      // Handle empty input - reset to 1
      _resetToDefaultServing();
      return;
    }

    final newServingSize = double.tryParse(text);
    if (newServingSize == null || newServingSize <= 0) {
      // Handle invalid input - reset to 1
      _resetToDefaultServing();
      return;
    }

    final currentServing = double.tryParse(widget.foodModel.serving ?? '1') ?? 1.0;
    if (newServingSize != currentServing) {
      _updateNutritionValues(newServingSize);
    }
  }

  void _onFocusChanged() {
    if (!_servingSizeFocusNode.hasFocus) {
      // When focus is lost, validate and update the serving size
      _validateAndUpdateServing(_servingSizeController.text);
    }
  }

  void _resetToDefaultServing() {
    final currentServing = double.tryParse(widget.foodModel.serving ?? '1') ?? 1.0;
    if (currentServing != 1.0) {
      _servingSizeController.text = '1';
      _updateNutritionValues(1.0);
    }
  }

  void _validateAndUpdateServing(String value) {
    if (value.isEmpty) {
      _resetToDefaultServing();
      return;
    }

    final newServingSize = double.tryParse(value);
    if (newServingSize == null || newServingSize <= 0) {
      // Invalid input, reset to current valid value
      final currentServing = double.tryParse(widget.foodModel.serving ?? '1') ?? 1.0;
      _servingSizeController.text = _formatServingSize(currentServing);
      return;
    }

    final currentServing = double.tryParse(widget.foodModel.serving ?? '1') ?? 1.0;
    if (newServingSize != currentServing) {
      // Update the text field with formatted value
      _servingSizeController.text = _formatServingSize(newServingSize);
      _updateNutritionValues(newServingSize);
    }
  }

  String _formatServingSize(double servingSize) {
    // Format to remove unnecessary decimal places
    if (servingSize == servingSize.roundToDouble()) {
      return servingSize.toInt().toString();
    } else {
      return servingSize.toStringAsFixed(1);
    }
  }

  void _updateNutritionValues(double servingSize) {
    // Calculate new nutrition values based on serving size using original values
    final newCalories = (_originalCalories * servingSize).round();
    final newProtein = _originalProtein * servingSize;
    final newCarbs = _originalCarbs * servingSize;
    final newFat = _originalFat * servingSize;

    // Get current food model from the bloc state to calculate differences
    final recentFoodState = context.read<RecentFoodBloc>().state;
    FoodModel? currentFoodModel;

    // Find the current food model in the state
    for (var food in recentFoodState.foodList) {
      if (food.tempId != null && food.tempId == widget.foodModel.tempId) {
        currentFoodModel = food;
        break;
      } else if (food.imagePath == widget.foodModel.imagePath) {
        currentFoodModel = food;
        break;
      } else if (food.id == widget.foodModel.id) {
        currentFoodModel = food;
        break;
      }
    }

    // Use current food model or fallback to widget food model
    currentFoodModel ??= widget.foodModel;

    // Calculate differences for database update
    final caloriesDiff = newCalories - (currentFoodModel.calories ?? 0);
    final proteinDiff = newProtein - (currentFoodModel.protein ?? 0.0);
    final carbsDiff = newCarbs - (currentFoodModel.carbs ?? 0.0);
    final fatDiff = newFat - (currentFoodModel.fat ?? 0.0);

    // Create updated food model with new serving size
    final updatedFoodModel = currentFoodModel.copyWith(
      calories: newCalories,
      protein: newProtein,
      carbs: newCarbs,
      fat: newFat,
      serving: servingSize.toString(),
    );

    // Update the bloc with differences
    context.read<RecentFoodBloc>().add(UpdateFoodWithDifferences(
      meal: updatedFoodModel,
      caloriesDiff: caloriesDiff.toDouble(),
      carbsDiff: carbsDiff,
      proteinDiff: proteinDiff,
      fatDiff: fatDiff,
    ));
  }

  void _navigateToEditValue(BuildContext context, NutritionType type, FoodModel foodModel) {
    double targetValue;

    switch (type) {
      case NutritionType.calories:
        targetValue = widget.targetCalories;
        break;
      case NutritionType.protein:
        targetValue = widget.targetProtein;
        break;
      case NutritionType.carbs:
        targetValue = widget.targetCarbs;
        break;
      case NutritionType.fat:
        targetValue = widget.targetFat;
        break;
    }
    final recentFoodBloc = context.read<RecentFoodBloc>();

    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (ctx, animation, secondaryAnimation) => BlocProvider.value(
          value: recentFoodBloc,
          child: EditValueScreen(
            foodModel: foodModel,
            nutritionType: type,
            targetValue: targetValue,
          ),
        ),
        transitionsBuilder: (ctx, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOutCubic;

          final tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));

          final offsetAnimation = animation.drive(tween);
          return SlideTransition(
            position: offsetAnimation,
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<FoodDatabaseBloc>(),
      child: BlocBuilder<RecentFoodBloc, RecentFoodState>(
        builder: (context, recentFoodState) {
          // Find the updated food model from the state
          FoodModel currentFoodModel = widget.foodModel;

          // Look for the updated food model in the state
          for (var food in recentFoodState.foodList) {
            if (food.tempId != null && food.tempId == widget.foodModel.tempId) {
              currentFoodModel = food;
              break;
            } else if (food.imagePath == widget.foodModel.imagePath) {
              currentFoodModel = food;
              break;
            } else if (food.id == widget.foodModel.id) {
              currentFoodModel = food;
              break;
            }
          }

          // Update serving size text field if the current model has a different serving size
          final currentServing = double.tryParse(currentFoodModel.serving ?? '1') ?? 1.0;
          final displayedServing = double.tryParse(_servingSizeController.text) ?? 1.0;
          if (currentServing != displayedServing && !_servingSizeFocusNode.hasFocus) {
            // Only update if the text field is not focused (to avoid interfering with user input)
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _servingSizeController.text = _formatServingSize(currentServing);
            });
          }

          return ListView(
            padding: const EdgeInsetsDirectional.symmetric(horizontal: 20, vertical: 30),
            controller: widget.controller,
            children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (currentFoodModel.date != null)
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: context.onPrimaryColor,
                  ),
                  padding: const EdgeInsetsDirectional.all(5),
                  child: Text(
                    DateFormat('h:mm a').format(currentFoodModel.date!),
                    style: context.textTheme.labelLarge!.copyWith(
                      color: context.onSecondary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: currentFoodModel.isHalal != false ? const Color(0xff27AE60) : Colors.red,
                ),
                padding: const EdgeInsetsDirectional.symmetric(horizontal: 20, vertical: 5),
                child: Text(
                  currentFoodModel.isHalal != false ? LocaleKeys.common_halal.tr() : LocaleKeys.common_haram.tr(),
                  style: context.textTheme.labelLarge!.copyWith(
                    color: context.onPrimaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  (context.locale == const Locale('ar') ? currentFoodModel.arabicName : currentFoodModel.englishName) ??
                      currentFoodModel.dish ??
                      'Unknown',
                  style: context.textTheme.titleMedium!.copyWith(
                    color: context.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Padding(
                padding: const EdgeInsetsDirectional.only(end: 20),
                child: BlocBuilder<FoodDatabaseBloc, FoodDatabaseState>(
                  builder: (context, state) {
                    return IconButton(
                      onPressed: () {
                        setState(() {
                          isSaved = !isSaved;
                        });
                        context.read<FoodDatabaseBloc>().add(
                              AddFoodEvent(
                                meal: DatabaseFoodModel(
                                  calories: currentFoodModel.calories,
                                  fat: currentFoodModel.fat,
                                  carbs: currentFoodModel.carbs,
                                  protein: currentFoodModel.protein,
                                  dish: currentFoodModel.dish,
                                  ingredients: currentFoodModel.ingredients,
                                  isFavoriteMeal: isSaved,
                                  isHalal: currentFoodModel.isHalal,
                                  date: DateTime.now(),
                                ),
                              ),
                            );
                      },
                      icon: Icon(
                        isSaved ? Icons.bookmark_outlined : Icons.bookmark_border_outlined,
                        color: context.primaryColor,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 18),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                LocaleKeys.common_servings.tr(),
                style: context.textTheme.titleSmall!.copyWith(
                  color: context.onSecondary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              GestureDetector(
                onTap: () {
                  _servingSizeFocusNode.requestFocus();
                },
                child: Container(
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      color: context.onPrimaryColor,
                      border: Border.all(color: context.onSecondary)),
                  padding: const EdgeInsetsDirectional.symmetric(horizontal: 30, vertical: 10),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        width: 30,
                        child: TextField(
                          controller: _servingSizeController,
                          focusNode: _servingSizeFocusNode,
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                          textAlign: TextAlign.center,
                          style: context.textTheme.bodyMedium!.copyWith(
                            color: context.onSecondary,
                            fontWeight: FontWeight.bold,
                          ),
                          decoration: const InputDecoration(
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.zero,
                            isDense: true,
                          ),
                          onSubmitted: (value) {
                            _servingSizeFocusNode.unfocus();
                            _validateAndUpdateServing(value);
                          },
                          onEditingComplete: () {
                            _validateAndUpdateServing(_servingSizeController.text);
                          },
                        ),
                      ),
                      const SizedBox(width: 10),
                      Icon(
                        Icons.mode_edit_outline,
                        color: context.onSecondary,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          AppGestureDetector(
              onTap: () {
                _navigateToEditValue(context, NutritionType.calories, currentFoodModel);
              },
              child: CalsContainer(value: currentFoodModel.calories)),

          const SizedBox(height: 10),

          // Clickable Macronutrients Row
          Row(
            spacing: 6,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: AppGestureDetector(
                  onTap: () {
                    _navigateToEditValue(context, NutritionType.protein, currentFoodModel);
                  },
                  child: IngredientCard(image: Assets.imagesProtien, item: LocaleKeys.common_protien.tr(), value: currentFoodModel.protein),
                ),
              ),
              Expanded(
                child: AppGestureDetector(
                  onTap: () {
                    _navigateToEditValue(context, NutritionType.fat, currentFoodModel);
                  },
                  child: IngredientCard(image: Assets.imagesFats, item: LocaleKeys.common_fat.tr(), value: currentFoodModel.fat),
                ),
              ),
              Expanded(
                child: AppGestureDetector(
                  onTap: () {
                    _navigateToEditValue(context, NutritionType.carbs, currentFoodModel);
                  },
                  child: IngredientCard(image: Assets.imagesCarbs, item: LocaleKeys.common_carbs.tr(), value: currentFoodModel.carbs),
                ),
              ),
            ],
          ),

          const SizedBox(height: 10),
          Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: context.onSecondary.withAlpha(29),
                width: 1,
              ),
              color: context.onPrimaryColor,
              borderRadius: BorderRadius.circular(12),
            ),
            padding: const EdgeInsetsDirectional.symmetric(horizontal: 10, vertical: 15),
            child: Row(
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: context.background,
                  ),
                  padding: const EdgeInsetsDirectional.all(10),
                  child: const AppImage.asset(
                    Assets.imagesBrokenHeart,
                    width: 25,
                    height: 25,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            LocaleKeys.common_health_score.tr(),
                            style: context.textTheme.labelLarge!.copyWith(
                              color: context.onSecondary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Text(
                            '7 / 10',
                            style: context.textTheme.bodySmall!.copyWith(
                              color: context.onSecondary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 10),
                      LinearProgressIndicator(
                        color: context.primaryColor,
                        value: .7,
                        backgroundColor: context.onSecondary.withAlpha(25),
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
          // const SizedBox(height: 14),
          // Text(
          //   'المكونات',
          //   style: context.textTheme.titleSmall!.copyWith(
          //     color: context.onSecondary,
          //     fontWeight: FontWeight.bold,
          //   ),
          // ),
          // const SizedBox(height: 8),
          // Row(
          //   children: [
          //     Container(
          //       decoration: BoxDecoration(
          //         border: Border.all(
          //           color: context.onSecondary.withAlpha(29),
          //           width: 1,
          //         ),
          //         color: context.onPrimaryColor,
          //         borderRadius: BorderRadius.circular(12),
          //       ),
          //       height: 80,
          //       width: 100,
          //       padding: const EdgeInsetsDirectional.symmetric(horizontal: 20, vertical: 10),
          //       child: Column(
          //         children: [
          //           Text(
          //             'اضافة',
          //             style: context.textTheme.labelLarge!.copyWith(
          //               color: context.onSecondary,
          //               fontWeight: FontWeight.w400,
          //             ),
          //           ),
          //           const SizedBox(height: 5),
          //           Icon(
          //             Icons.add,
          //             size: 20,
          //             color: context.onSecondary,
          //           ),
          //         ],
          //       ),
          //     ),
          //   ],
          // ),
            ],
          );
        },
      ),
    );
  }
}
