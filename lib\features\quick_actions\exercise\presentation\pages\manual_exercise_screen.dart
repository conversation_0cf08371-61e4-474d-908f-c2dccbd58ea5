import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/core/di/injection.dart';
import 'package:cal/features/quick_actions/exercise/data/models/exercise_save_model.dart';
import 'package:cal/features/quick_actions/exercise/presentation/bloc/exercise_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ManualExerciseScreen extends StatefulWidget {
  const ManualExerciseScreen({super.key});

  @override
  State<ManualExerciseScreen> createState() => _ManualExerciseScreenState();
}

class _ManualExerciseScreenState extends State<ManualExerciseScreen> {
  final TextEditingController _caloriesController = TextEditingController();

  @override
  void dispose() {
    _caloriesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ExerciseBloc>(
      create: (context) => getIt<ExerciseBloc>(),
      child: BlocListener<ExerciseBloc, ExerciseState>(
        listener: (context, state) {
          if (state is ExerciseSaved) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Exercise saved successfully!')),
            );
            Navigator.of(context).pop();
          } else if (state is ExerciseError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Error: ${state.message}')),
            );
          }
        },
        child: Scaffold(
          appBar: AppBar(
            title: AppText.titleLarge(
              'Log Exercise(Manual)',
              color: context.onSecondary,
            ),
            centerTitle: true,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ),
          body: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppText.titleLarge(
                  'ادخال يدوي',
                  color: context.onSecondary,
                  fontWeight: FontWeight.bold,
                ),
                const SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _caloriesController,
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          hintText: 'السعرات المستهدفة',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: context.primaryColor),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: context.primaryColor, width: 2),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: context.primaryColor,
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: AppText.titleLarge(
                          _caloriesController.text.isEmpty ? '0' : _caloriesController.text,
                          color: context.onPrimaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      final calories = int.tryParse(_caloriesController.text) ?? 0;
                      final exercise = ExerciseSaveModel(
                        type: 'Manual',
                        calories: calories,
                        intensity: 'none', // Manual entry doesn't have intensity
                        duration: 0, // Manual entry doesn't have duration
                      );
                      context.read<ExerciseBloc>().add(SaveExercise(exercise: exercise));
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.primaryColor,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: AppText.titleLarge(
                      'اضافة',
                      color: context.onPrimaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
