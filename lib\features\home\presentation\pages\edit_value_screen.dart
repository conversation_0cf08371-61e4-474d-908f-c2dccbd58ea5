import 'dart:developer';

import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/widgets/metric_card.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/features/home/<USER>/bloc/recent_food_bloc.dart';
import 'package:cal/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

enum NutritionType {
  calories,
  protein,
  carbs,
  fat,
}

class EditValueScreen extends StatefulWidget {
  final FoodModel foodModel;
  final NutritionType nutritionType;
  final double targetValue;

  const EditValueScreen({
    super.key,
    required this.foodModel,
    required this.nutritionType,
    required this.targetValue,
  });

  @override
  State<EditValueScreen> createState() => _EditValueScreenState();
}

class _EditValueScreenState extends State<EditValueScreen> {
  late TextEditingController _valueController;
  late FocusNode _focusNode;
  bool _hasChanges = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _valueController = TextEditingController(text: _getCurrentValue().toString());
    _focusNode = FocusNode();
    _valueController.addListener(_onValueChanged);
  }

  void _onValueChanged() {
    final newValue = double.tryParse(_valueController.text) ?? 0.0;
    final currentValue = _getCurrentValue();
    setState(() {
      _hasChanges = newValue != currentValue;
    });
  }

  double _getCurrentValue() {
    switch (widget.nutritionType) {
      case NutritionType.calories:
        return widget.foodModel.calories?.toDouble() ?? 0.0;
      case NutritionType.protein:
        return widget.foodModel.protein ?? 0.0;
      case NutritionType.carbs:
        return widget.foodModel.carbs ?? 0.0;
      case NutritionType.fat:
        return widget.foodModel.fat ?? 0.0;
    }
  }

  String _getNutritionTitle() {
    switch (widget.nutritionType) {
      case NutritionType.calories:
        return 'السعرات الحرارية';
      case NutritionType.protein:
        return 'البروتين';
      case NutritionType.carbs:
        return 'الكربوهيدرات';
      case NutritionType.fat:
        return 'الدهون';
    }
  }

  String _getNutritionUnit() {
    switch (widget.nutritionType) {
      case NutritionType.calories:
        return 'kcal';
      case NutritionType.protein:
      case NutritionType.carbs:
      case NutritionType.fat:
        return 'g';
    }
  }

  String _getNutritionIcon() {
    switch (widget.nutritionType) {
      case NutritionType.calories:
        return Assets.imagesCals;
      case NutritionType.protein:
        return Assets.imagesProtien;
      case NutritionType.carbs:
        return Assets.imagesCarbs;
      case NutritionType.fat:
        return Assets.imagesFats;
    }
  }

  Color _getNutritionColor() {
    switch (widget.nutritionType) {
      case NutritionType.calories:
        return const Color(0xffFF6B6B);
      case NutritionType.protein:
        return const Color(0xff4ECDC4);
      case NutritionType.carbs:
        return const Color(0xffFFE66D);
      case NutritionType.fat:
        return const Color(0xffA8E6CF);
    }
  }

  void _saveChanges() async {
    if (!_hasChanges) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final newValue = double.tryParse(_valueController.text) ?? 0.0;
      final originalValue = _getCurrentValue();

      // Calculate the difference for the specific nutrition type
      double caloriesDiff = 0.0;
      double proteinDiff = 0.0;
      double carbsDiff = 0.0;
      double fatDiff = 0.0;

      FoodModel updatedFoodModel;

      switch (widget.nutritionType) {
        case NutritionType.calories:
          caloriesDiff = newValue - originalValue;
          updatedFoodModel = widget.foodModel.copyWith(calories: newValue.toInt());
          break;
        case NutritionType.protein:
          proteinDiff = newValue - originalValue;
          updatedFoodModel = widget.foodModel.copyWith(protein: newValue);
          break;
        case NutritionType.carbs:
          carbsDiff = newValue - originalValue;
          updatedFoodModel = widget.foodModel.copyWith(carbs: newValue);
          break;
        case NutritionType.fat:
          fatDiff = newValue - originalValue;
          updatedFoodModel = widget.foodModel.copyWith(fat: newValue);
          break;
      }

      log('EditValueScreen: Updated food model date: ${updatedFoodModel.date}');
      log('EditValueScreen: Nutrition differences - Calories: $caloriesDiff, Protein: $proteinDiff, Carbs: $carbsDiff, Fat: $fatDiff');

      // Use the new event with calculated differences
      context.read<RecentFoodBloc>().add(UpdateFoodWithDifferences(
        meal: updatedFoodModel,
        caloriesDiff: caloriesDiff,
        carbsDiff: carbsDiff,
        proteinDiff: proteinDiff,
        fatDiff: fatDiff,
      ));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
    context.pop();
  }

  @override
  void dispose() {
    _valueController.dispose();
    _focusNode.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentValue = double.tryParse(_valueController.text) ?? 0.0;
    final progress = calculateSafeProgress(widget.targetValue, currentValue);

    return Scaffold(
      backgroundColor: context.background,
      appBar: AppBar(
        backgroundColor: context.background,
        elevation: 0,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: Icon(
            Icons.arrow_back,
            color: context.primaryColor,
          ),
        ),
        title: Text(
          'تعديل ${_getNutritionTitle()}',
          style: context.textTheme.titleLarge!.copyWith(
            color: context.primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          if (_hasChanges)
            TextButton(
              onPressed: _isLoading ? null : _saveChanges,
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : Text(
                      'حفظ',
                      style: TextStyle(
                        color: context.primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // MetricCard showing target vs current
              MetricCard(
                title: currentValue.toInt().toString(),
                description: '${_getNutritionTitle()} (${widget.targetValue.toInt()} هدف)',
                icon: _getNutritionIcon(),
                color: _getNutritionColor(),
                progress: progress,
                progressHeight: 60,
                progressWidth: 60,
                horizontal: true,
              ),


              const SizedBox(height: 15),

              // Text field for editing
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  color: context.onPrimaryColor,
                  boxShadow: [
                    BoxShadow(
                      color: context.onSecondary.withAlpha(25),
                      offset: const Offset(0, 2),
                      blurRadius: 8,
                    ),
                  ],
                ),
                child: TextFormField(
                  controller: _valueController,
                  focusNode: _focusNode,
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                  ],
                  style: context.textTheme.headlineMedium!.copyWith(
                    color: _getNutritionColor(),
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                      borderSide: BorderSide(
                        color: _getNutritionColor(),
                        width: 2,
                      ),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 20,
                    ),
                    suffixText: _getNutritionUnit(),
                    suffixStyle: context.textTheme.titleLarge!.copyWith(
                      color: context.onSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                    hintText: 'أدخل القيمة',
                    hintStyle: context.textTheme.titleMedium!.copyWith(
                      color: context.onSecondary.withAlpha(100),
                    ),
                  ),
                ),
              ),

              

              const Spacer(),

              // Save button (full width)
              if (_hasChanges)
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveChanges,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _getNutritionColor(),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            'حفظ التغييرات',
                            style: context.textTheme.titleMedium!.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
