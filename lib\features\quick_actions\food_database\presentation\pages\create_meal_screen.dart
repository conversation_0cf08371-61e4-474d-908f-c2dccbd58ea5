import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/theme/text_theme.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/common/widgets/large_button.dart';
import 'package:cal/common/widgets/manual_nutrition_edit_screen.dart';
import 'package:cal/core/di/injection.dart';
import 'package:cal/features/onboarding/presentation/widgets/onboarding_metric_card.dart';
import 'package:cal/features/quick_actions/food_database/data/models/database_food_model.dart';
import 'package:cal/features/quick_actions/food_database/presentation/bloc/food_database_bloc.dart';
import 'package:cal/features/quick_actions/food_database/presentation/pages/database_list_screen.dart';
import 'package:cal/features/quick_actions/food_database/presentation/widgets/enter_your_food_container.dart';
import 'package:cal/features/quick_actions/food_database/presentation/widgets/food_database_card.dart';
import 'package:cal/features/quick_actions/food_database/presentation/widgets/nutristion_container.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CreateMealScreen extends StatefulWidget {
  const CreateMealScreen({super.key});

  @override
  State<CreateMealScreen> createState() => _CreateMealScreenState();
}

class _CreateMealScreenState extends State<CreateMealScreen> {
  List<DatabaseFoodModel> selectedFoods = [];
  TextEditingController mealName = TextEditingController();
  String mealCals = "0";
  String mealProtien = "0";
  String mealCarb = "0";
  String mealFat = "0";

  bool isEditing = false;
  FocusNode focusNode = FocusNode();

  int cals = 0;
  double carbs = 0;
  double fat = 0;
  double protein = 0;

  @override
  Widget build(BuildContext context) {
    return BlocProvider<FoodDatabaseBloc>(
      create: (context) => getIt<FoodDatabaseBloc>(),
      child: Scaffold(
        backgroundColor: context.background,
        appBar: AppBar(
          forceMaterialTransparency: true,
          elevation: 0,
          centerTitle: true,
          toolbarHeight: 80,
          leading: Padding(
            padding: const EdgeInsets.only(right: 18.0),
            child: GestureDetector(
                onTap: () {
                  context.pop();
                },
                child: const Icon(Icons.arrow_back)),
          ),
          automaticallyImplyLeading: false,
          titleSpacing: 0,
          title: Text(
            LocaleKeys.food_database_create_meal.tr(),
            style: context.textTheme.headlineMedium!.copyWith(fontWeight: FontWeight.w300, color: context.primaryColor),
            textAlign: TextAlign.start,
          ),
        ),
        body: Padding(
          padding: const EdgeInsetsDirectional.only(end: 26, start: 26, top: 2, bottom: 20),
          child: Column(
            // mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  spacing: 8,
                  children: [
                    TextFormField(
                      controller: mealName,
                      cursorColor: context.primaryColor,
                      maxLines: 1,
                      focusNode: focusNode,
                      onTap: () {
                        setState(() {
                          isEditing = true;
                        });
                      },
                      textAlign: TextAlign.start,
                      decoration: InputDecoration(
                        hintStyle: context.textTheme.bodySmall!.copyWith(color: context.onSecondary),
                        hintText: LocaleKeys.food_database_enter_name.tr(),
                        filled: true,
                        suffixIcon: Icon(Icons.edit, color: context.onSecondary),
                        fillColor: context.onPrimaryColor,
                        border: OutlineInputBorder(
                          borderRadius: const BorderRadius.all(Radius.circular(12)),
                          borderSide: BorderSide(color: context.primaryColor, width: 1),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: const BorderRadius.all(Radius.circular(12)),
                          borderSide: BorderSide(color: context.primaryColor, width: 1),
                        ),
                        enabledBorder: const OutlineInputBorder(
                          borderRadius: BorderRadius.all(Radius.circular(12)),
                        ),
                        errorBorder: OutlineInputBorder(
                          borderRadius: const BorderRadius.all(Radius.circular(12)),
                          borderSide: BorderSide(color: context.primaryColor, width: 1),
                        ),
                        disabledBorder: OutlineInputBorder(
                          borderRadius: const BorderRadius.all(Radius.circular(12)),
                          borderSide: BorderSide(color: context.primaryColor, width: 1),
                        ),
                      ),
                      style: context.textTheme.bodySmall!.copyWith(
                        fontWeight: FontWeight.w900,
                        color: context.onSecondary,
                      ),
                    ),
                    BlocBuilder<FoodDatabaseBloc, FoodDatabaseState>(
                      builder: (context, state) {
                        cals = state.selectedFoods.fold<int>(cals, (previousValue, element) => previousValue + (element.calories ?? 0));
                        return AppGestureDetector(
                          onTap: () async {
                            context.push(ManualNutritionEditScreen(
                              onSave: (value) {
                                setState(() {
                                  cals = int.parse(value);
                                });
                              },
                              nutritionValue: cals.toString(),
                              cardType: CardType.cals,
                            ));
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 10),
                            decoration: BoxDecoration(
                              color: context.onPrimaryColor,
                              border: Border.all(color: context.onSecondary.withAlpha(50), width: 1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              spacing: 12,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(12),
                                    color: context.onPrimaryContainer.withAlpha(210),
                                  ),
                                  child: const AppImage.asset(Assets.imagesCals),
                                ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      LocaleKeys.common_calories.tr(),
                                      textAlign: TextAlign.center,
                                      style: textTheme.bodyMedium!.copyWith(color: Theme.of(context).colorScheme.onSecondary, fontWeight: FontWeight.bold, fontSize: 10),
                                    ),
                                    Text(
                                      cals.toString(),
                                      textAlign: TextAlign.start,
                                      style: textTheme.bodyMedium!.copyWith(color: context.colorScheme.primary, fontWeight: FontWeight.bold, fontSize: 24),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                    BlocBuilder<FoodDatabaseBloc, FoodDatabaseState>(
                      builder: (context, state) {
                        protein = state.selectedFoods.fold<double>(protein, (previousValue, element) => previousValue + (element.protein ?? 0));
                        carbs = state.selectedFoods.fold<double>(carbs, (previousValue, element) => previousValue + (element.carbs ?? 0));
                        fat = state.selectedFoods.fold<double>(fat, (previousValue, element) => previousValue + (element.fat ?? 0));
                        return Row(
                          spacing: 6,
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            NutristionContainer(
                              value: protein.toString(),
                              cardType: CardType.protien,
                              onTap: () async {
                                context.push(ManualNutritionEditScreen(
                                  onSave: (value) {
                                    setState(() {
                                      protein = double.parse(value);
                                    });
                                  },
                                  nutritionValue: protein.toString(),
                                  cardType: CardType.protien,
                                ));
                              },
                            ),
                            NutristionContainer(
                              value: carbs.toString(),
                              cardType: CardType.carbs,
                              onTap: () async {
                                context.push(ManualNutritionEditScreen(
                                  onSave: (value) {
                                    setState(() {
                                      carbs = double.parse(value);
                                    });
                                  },
                                  nutritionValue: carbs.toString(),
                                  cardType: CardType.carbs,
                                ));
                              },
                            ),
                            NutristionContainer(
                              value: fat.toString(),
                              cardType: CardType.fat,
                              onTap: () async {
                                context.push(ManualNutritionEditScreen(
                                  onSave: (value) {
                                    setState(() {
                                      fat = double.parse(value);
                                    });
                                  },
                                  nutritionValue: fat.toString(),
                                  cardType: CardType.fat,
                                ));
                              },
                            ),
                          ],
                        );
                      },
                    ),
                    BlocBuilder<FoodDatabaseBloc, FoodDatabaseState>(
                      builder: (context, state) => state.selectedFoods.isNotEmpty
                          ? const SizedBox.shrink()
                          : Column(
                              children: [
                                const SizedBox(height: 50),
                                const AppImage.asset(
                                  Assets.onboardingPlate,
                                  size: 35,
                                ),
                                AppText.displaySmall(
                                  LocaleKeys.food_database_meal_nutarians.tr(),
                                  fontWeight: FontWeight.bold,
                                ),
                              ],
                            ),
                    ),
                    const SizedBox(height: 10),
                    BlocBuilder<FoodDatabaseBloc, FoodDatabaseState>(
                      builder: (context, state) {
                        return EnterYourFoodContainer(
                          onPressed: () async {
                            context.push(
                              BlocProvider.value(
                                value: context.read<FoodDatabaseBloc>(),
                                child: const DatabaseListScreen(),
                              ),
                            );
                          },
                          verticalaPadding: 15,
                          text: LocaleKeys.food_database_add_nutrians_to_this_meal.tr(),
                          icon: const Icon(Icons.add),
                        );
                      },
                    ),
                    Expanded(
                      child: BlocBuilder<FoodDatabaseBloc, FoodDatabaseState>(
                        builder: (context, state) {
                          return SingleChildScrollView(
                            child: Column(
                              children: state.selectedFoods.map((food) {
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 10.0),
                                  child: FoodDatabaseCard(
                                    title: food.dish ?? "Unknown",
                                    cals: food.calories.toString(),
                                    onAddTap: () {},
                                  ),
                                );
                              }).toList(),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
              BlocBuilder<FoodDatabaseBloc, FoodDatabaseState>(
                builder: (context, state) {
                  return LargeButton(
                    onPressed: () {
                      context.read<FoodDatabaseBloc>().add(
                            AddFoodEvent(
                              meal: DatabaseFoodModel(
                                calories: state.selectedFoods.fold<int>(0, (previousValue, element) => previousValue + (element.calories ?? 0)),
                                carbs: state.selectedFoods.fold<double>(0, (previousValue, element) => previousValue + (element.carbs ?? 0)),
                                fat: state.selectedFoods.fold<double>(0, (previousValue, element) => previousValue + (element.fat ?? 0)),
                                protein: state.selectedFoods.fold<double>(0, (previousValue, element) => previousValue + (element.protein ?? 0)),
                                date: DateTime.now(),
                                isMealCreated: true,
                                ingredients: state.selectedFoods.map((e) => e.dish ?? '').toList(),
                                dish: mealName.text,
                              ),
                            ),
                          );
                      context.pop();
                    },
                    text: LocaleKeys.food_database_save.tr(),
                    backgroundColor: context.primaryColor,
                    textStyle: TextStyle(color: context.onPrimaryColor, fontWeight: FontWeight.bold, fontSize: 16),
                    circularRadius: 12,
                  );
                },
              )
            ],
          ),
        ),
      ),
    );
  }
}
