import 'dart:developer';

import 'package:cal/common/utils/shared_preferences_helper.dart';
import 'package:cal/core/local_models/daily_data_model/daily_data_model.dart';
import 'package:injectable/injectable.dart';
import 'package:isar/isar.dart';

import '../../../common/consts/app_keys.dart';
import '../../di/injection.dart';

@lazySingleton
class DailyUserInfoService {
  static Future<void> initDailyData(DateTime targetDate) async {
    try {
      final isar = getIt<Isar>();
      log('initDailyData started for targetDate: $targetDate');

      final res = await isar.dailyUserDataModels.where().sortByDate().findAll();
      log('Fetched ${res.length} existing daily data records from Isar');

      final targetFriday = _getWeekFriday(targetDate);
      final startDate = targetFriday.subtract(const Duration(days: 27));
      final today = DateTime.now();

      log('Calculated targetFriday: $targetFriday, startDate (4 weeks before): $startDate, today: $today');

      final lastValid = _getLastValidData(res);
      if (lastValid != null) {
        log('Last valid data found with date: ${lastValid.date}');
      } else {
        log('No valid last data found, will fallback to SharedPreferences or defaults');
      }

      final List<DailyUserDataModel> toSave = [];
      int createdCount = 0;
      int updatedCount = 0;
      int skippedCount = 0;

      DateTime currentDate = startDate;
      while (!currentDate.isAfter(targetFriday)) {
        final matching = res.where((e) => _isSameDay(e.date, currentDate));
        final existing = matching.isNotEmpty ? matching.first : null;

        final isFuture = currentDate.isAfter(today);

        if (existing != null) {
          final isToday = _isSameDay(currentDate, today);

          final hasZeroValues =
              existing.targetCalories == 0 && existing.targetCarbs == 0 && existing.targetFat == 0 && existing.targetProtein == 0;

          if (isToday && hasZeroValues) {
            final updated = existing.copyWith(
              targetCalories: lastValid?.targetCalories ?? existing.targetCalories,
              targetCarbs: lastValid?.targetCarbs ?? existing.targetCarbs,
              targetFat: lastValid?.targetFat ?? existing.targetFat,
              targetProtein: lastValid?.targetProtein ?? existing.targetProtein,
              consumedCalories: 0,
              consumedCarbs: 0,
              consumedFat: 0,
              consumedProtein: 0,
              weight: lastValid?.weight ?? existing.weight,
              height: lastValid?.height ?? existing.height,
              bmi: lastValid?.bmi ?? existing.bmi,
            );
            toSave.add(updated);
            updatedCount++;
            log('Updated today\'s data for date $currentDate with last valid values');
          } else {
            // Already valid data, skip updating
            skippedCount++;
            log('Skipped existing valid data for date $currentDate');
          }
        } else {
          final newData = isFuture ? _createZeroedModel(currentDate) : _createModelFromValidData(currentDate, lastValid);
          toSave.add(newData);
          createdCount++;
          log('Created new data for date $currentDate (future: $isFuture)');
        }

        currentDate = currentDate.add(const Duration(days: 1));
      }

      if (toSave.isNotEmpty) {
        await isar.writeTxn(() async {
          await isar.dailyUserDataModels.putAllByDate(toSave);
        });
        log('Saved ${toSave.length} records to Isar');
      } else {
        log('No records to save');
      }

      log('initDailyData summary: created $createdCount, updated $updatedCount, skipped $skippedCount');
    } catch (e, st) {
      log('initDailyData error: $e\n$st');
    }
  }

  // Get the Friday of the week
  static DateTime _getWeekFriday(DateTime date) {
    return date.add(Duration(days: (DateTime.friday - date.weekday + 7) % 7));
  }

  // Check if two dates are on the same day
  static bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  // Get the last valid (non-zero) model
  static DailyUserDataModel? _getLastValidData(List<DailyUserDataModel> entries) {
    final filtered = entries.reversed.where(
      (e) => e.targetCalories > 0 && e.targetCarbs > 0 && e.targetFat > 0 && e.targetProtein > 0,
    );
    return filtered.isNotEmpty ? filtered.first : null;
  }

  // Safely parse from prefs
  static double _getPrefDouble(String key, double fallback) {
    final raw = ShPH.getData(key: key);
    if (raw is double) return raw;
    if (raw is int) return raw.toDouble();
    if (raw is String) return double.tryParse(raw) ?? fallback;
    return fallback;
  }

  // Get model from last valid or fallback
  static DailyUserDataModel _createModelFromValidData(DateTime date, DailyUserDataModel? base) {
    if (base != null) {
      return DailyUserDataModel(
        date: date,
        targetCalories: base.targetCalories,
        targetCarbs: base.targetCarbs,
        targetFat: base.targetFat,
        targetProtein: base.targetProtein,
        consumedCalories: 0,
        consumedCarbs: 0,
        consumedFat: 0,
        consumedProtein: 0,
        weight: base.weight,
        height: base.height,
        bmi: base.bmi,
      );
    }

    final weight = _getPrefDouble(AppKeys.weight, 70);
    final height = _getPrefDouble(AppKeys.height, 170);

    return DailyUserDataModel(
      date: date,
      targetCalories: _getPrefDouble(AppKeys.cals, 2000),
      targetCarbs: _getPrefDouble(AppKeys.carbs, 250),
      targetFat: _getPrefDouble(AppKeys.fat, 70),
      targetProtein: _getPrefDouble(AppKeys.protien, 100),
      consumedCalories: 0,
      consumedCarbs: 0,
      consumedFat: 0,
      consumedProtein: 0,
      weight: weight,
      height: height,
      bmi: calculateBMI(weight, height),
    );
  }

  // Create zeroed model (used for future dates)
  static DailyUserDataModel _createZeroedModel(DateTime date) {
    return DailyUserDataModel(
      date: date,
      targetCalories: 0,
      targetCarbs: 0,
      targetFat: 0,
      targetProtein: 0,
      consumedCalories: 0,
      consumedCarbs: 0,
      consumedFat: 0,
      consumedProtein: 0,
      weight: 0,
      height: 0,
      bmi: 0,
    );
  }

  static Future<List<DailyUserDataModel>> getDailyDataForWeeks({required DateTime startDate}) async {
    final isar = getIt<Isar>();
    final res = await isar.dailyUserDataModels
        .where()
        .filter()
        .dateBetween(startDate, startDate.add(const Duration(days: 6)))
        .sortByDate()
        .findAll();
    return res;
  }

  static Future<DailyUserDataModel> getDailyBmi({required DateTime startDate}) async {
    final isar = getIt<Isar>();
    final res = await isar.dailyUserDataModels.where().filter().dateBetween(startDate, startDate.add(const Duration(days: 1))).findFirst();
    return res!;
  }

  static Future<List<DailyUserDataModel>> getDailyDataForMonths({int? days}) async {
    final isar = getIt<Isar>();
    final res = days != null
        ? await isar.dailyUserDataModels
            .where()
            .filter()
            .dateBetween(DateTime.now().add(Duration(days: -days)), DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day))
            .sortByDate()
            .findAll()
        : await isar.dailyUserDataModels.where().findAll();
    return res;
  }

  static Future<void> saveDailyData(DailyUserDataModel data) async {
    final isar = getIt<Isar>();

    await isar.writeTxn(() async {
      await isar.dailyUserDataModels.putByDate(data);
    });
  }


  static double calculateBMI(double weight, double height) {
    if (height == 0) return 0;
    return weight / ((height / 100) * (height / 100));
  }
}

extension DateUtils on DateTime {
  DateTime get toMidnight => DateTime(year, month, day);

  DateTime get endOfWeek {
    final diff = 7 - weekday;
    return toMidnight.add(Duration(days: diff));
  }
}
