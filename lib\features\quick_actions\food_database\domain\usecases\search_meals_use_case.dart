import 'package:cal/common/consts/typedef.dart';
import 'package:cal/features/quick_actions/food_database/data/models/remote_food_database_model.dart';
import 'package:injectable/injectable.dart';

import '../repositories/food_database_repository.dart';

@lazySingleton
class SearchMealsUseCase implements UseCase<List<RemoteFoodDatabaseModel>, SearchMealsParams> {

  final FoodDatabaseRepository foodDatabaseRepository;

  SearchMealsUseCase({required this.foodDatabaseRepository});

  @override
  DataResponse<List<RemoteFoodDatabaseModel>> call(SearchMealsParams params) {
    return foodDatabaseRepository.searchMeals(params);
  }
}

class SearchMealsParams with Params{

  final String query;

  @override
  QueryParams getParams() => {
    "searchQuery": query
  };

  SearchMealsParams({required this.query});

}