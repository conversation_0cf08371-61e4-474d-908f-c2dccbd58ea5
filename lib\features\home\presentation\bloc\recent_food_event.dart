part of 'recent_food_bloc.dart';

sealed class RecentFoodEvent extends Equatable {
  const RecentFoodEvent();

  @override
  List<Object?> get props => [];
}

class LoadFood extends RecentFoodEvent {
  final DateTime date;

  const LoadFood({required this.date});
}

class AddFood extends RecentFoodEvent {
  final FoodModel meal;
  final bool isFromSearch;

  const AddFood({required this.meal, this.isFromSearch = false});

  @override
  List<Object?> get props => [meal];
}

class UpdateFood extends RecentFoodEvent {
  final FoodModel meal;

  const UpdateFood(this.meal);

  @override
  List<Object?> get props => [meal];
}

class UpdateFoodWithDifferences extends RecentFoodEvent {
  final FoodModel meal;
  final double caloriesDiff;
  final double carbsDiff;
  final double proteinDiff;
  final double fatDiff;

  const UpdateFoodWithDifferences({
    required this.meal,
    required this.caloriesDiff,
    required this.carbsDiff,
    required this.proteinDiff,
    required this.fatDiff,
  });

  @override
  List<Object?> get props => [meal, caloriesDiff, carbsDiff, proteinDiff, fatDiff];
}

class ClearFood extends RecentFoodEvent {}

class DeleteFood extends RecentFoodEvent {
  final FoodModel meal;

  const DeleteFood(this.meal);

  @override
  List<Object?> get props => [meal];
}
