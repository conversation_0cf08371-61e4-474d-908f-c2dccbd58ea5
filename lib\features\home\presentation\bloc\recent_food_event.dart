part of 'recent_food_bloc.dart';

sealed class RecentFoodEvent extends Equatable {
  const RecentFoodEvent();

  @override
  List<Object?> get props => [];
}

class LoadFood extends RecentFoodEvent {
  final DateTime date;

  const LoadFood({required this.date});
}

class AddFood extends RecentFoodEvent {
  final FoodModel meal;
  final bool isFromSearch;

  const AddFood({required this.meal, this.isFromSearch = false});

  @override
  List<Object?> get props => [meal];
}

class UpdateFood extends RecentFoodEvent {
  final FoodModel meal;

  const UpdateFood(this.meal);

  @override
  List<Object?> get props => [meal];
}

class ClearFood extends RecentFoodEvent {}

class DeleteFood extends RecentFoodEvent {
  final FoodModel meal;

  const DeleteFood(this.meal);

  @override
  List<Object?> get props => [meal];
}
