import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/core/local_models/streak_model/streak_model.dart';
import 'package:cal/core/local_models/weigh_history_model/weight_history_model.dart';
import 'package:cal/features/quick_actions/food_database/data/models/database_food_model.dart';
import 'package:injectable/injectable.dart';
import 'package:isar/isar.dart';
import 'package:path_provider/path_provider.dart';
import 'package:cal/core/local_models/user_model/user_model.dart';

import 'local_models/daily_data_model/daily_data_model.dart';

@module
abstract class RegisterModule {
  @preResolve
  Future<Isar> get isar async {
    final dir = await getApplicationDocumentsDirectory();
    return await Isar.open(
      [
        FoodModelSchema,
        DatabaseFoodModelSchema,
        UserModelSchema,
        StreakModelSchema,
        DailyUserDataModelSchema,
        WeightHistoryModelSchema,
      ],
      directory: dir.path,
      name: 'isar',
      inspector: true,
    );
  }
}
