import 'package:cal/features/home/<USER>/usecases/get_daily_user_data_usecase.dart';
import 'package:cal/features/home/<USER>/usecases/update_daily_user_data_usecase.dart';
import 'package:get_it/get_it.dart';

void homeInjection(GetIt sl) {
  // UseCases
  sl.registerLazySingleton<GetDailyUserDataUseCase>(
    () => GetDailyUserDataUseCase(repository: sl()),
  );
  sl.registerLazySingleton<UpdateDailyUserDataUseCase>(
    () => UpdateDailyUserDataUseCase(repository: sl()),
  );
}
