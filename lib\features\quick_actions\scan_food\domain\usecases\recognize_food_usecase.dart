import 'dart:io';

import 'package:cal/common/consts/typedef.dart';
import 'package:cal/core/mobile_id_helper.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/features/quick_actions/scan_food/domain/repositories/scan_food_repository.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class AnalyzeFoodUseCase {
  final ScanFoodRepository repository;

  AnalyzeFoodUseCase(this.repository);

  DataResponse<FoodModel> call({required AnalyzeFoodParams analyzeFoodParams}) {
    return repository.recognizeFood(analyzeFoodParams: analyzeFoodParams);
  }
}

class AnalyzeFoodParams with Params {
  final String mobileID;
  final File imageFile;
  final String? prompt;

  AnalyzeFoodParams._({
    required this.mobileID,
    required this.imageFile,
    this.prompt,
  });

  static Future<AnalyzeFoodParams> create({
    required File imageFile,
    String? prompt,
  }) async {
    final id = await MobileIdHelper.getMobileId();
    return AnalyzeFoodParams._(
      mobileID: id,
      imageFile: imageFile,
      prompt: prompt,
    );
  }

  @override
  BodyMap getBody() => {
        "image": imageFile,
      };
}
