import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/theme/text_theme.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/features/onboarding/presentation/widgets/onboarding_metric_card.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class NutristionContainer extends StatefulWidget {
  final CardType cardType;
  final Function()? onTap;
  final String value;
  const NutristionContainer({super.key, required this.cardType, this.onTap, required this.value});

  @override
  State<NutristionContainer> createState() => _NutristionContainerState();
}

class _NutristionContainerState extends State<NutristionContainer> {
  String getTitle(CardType cardType) {
    switch (cardType) {
      case CardType.protien:
        return LocaleKeys.progress_protein.tr();
      case CardType.carbs:
        return LocaleKeys.progress_carbs.tr();
      case CardType.cals:
      case CardType.fat:
        return LocaleKeys.progress_fats.tr();
    }
  }

  String getIcon(CardType cardType) {
    switch (cardType) {
      case CardType.protien:
        return Assets.imagesProtien;
      case CardType.carbs:
        return Assets.imagesCarbs;
      case CardType.cals:
      case CardType.fat:
        return Assets.imagesFats;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: AppGestureDetector(
        onTap: widget.onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 10),
          decoration: BoxDecoration(
            color: context.onPrimaryColor,
            border: Border.all(color: context.onSecondary.withAlpha(30), width: 1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(3),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  spacing: 4,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(3),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: context.onPrimaryContainer.withAlpha(210),
                      ),
                      child: AppImage.asset(
                        getIcon(widget.cardType),
                        size: 10,
                      ),
                    ),
                    Text(
                      getTitle(widget.cardType),
                      textAlign: TextAlign.center,
                      style: textTheme.bodyMedium!.copyWith(
                          color: Theme.of(context).colorScheme.onSecondary, fontWeight: FontWeight.bold, fontSize: 12),
                    ),
                  ],
                ),
                const SizedBox(height: 5),
                Text(
                  "${widget.value}${LocaleKeys.onboarding_gram.tr()}",
                  textAlign: TextAlign.center,
                  style: textTheme.bodyMedium!
                      .copyWith(color: context.colorScheme.primary, fontWeight: FontWeight.bold, fontSize: 20),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
