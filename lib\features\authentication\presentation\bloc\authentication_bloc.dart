import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/entities/user_entity.dart';
import '../../domain/usecases/sign_in_with_apple_usecase.dart';
import '../../domain/usecases/sign_in_with_google_usecase.dart';
import '../../domain/usecases/sign_out_usecase.dart';
import '../../domain/usecases/get_current_user_usecase.dart';
import 'authentication_event.dart';
part 'authentication_state.dart';

// Authentication BLoC
class AuthenticationBloc extends Bloc<AuthenticationEvent, AuthenticationState> {
  final SignInWithAppleUseCase _signInWithAppleUseCase;
  final SignInWithGoogleUseCase _signInWithGoogleUseCase;
  final SignOutUseCase _signOutUseCase;
  
  final GetCurrentUserUseCase _getCurrentUserUseCase;

  AuthenticationBloc({
    required SignInWithAppleUseCase signInWithAppleUseCase,
    required SignInWithGoogleUseCase signInWithGoogleUseCase,
    required SignOutUseCase signOutUseCase,
    required GetCurrentUserUseCase getCurrentUserUseCase,
  }) : _signInWithAppleUseCase = signInWithAppleUseCase,
       _signInWithGoogleUseCase = signInWithGoogleUseCase,
       _signOutUseCase = signOutUseCase,
       _getCurrentUserUseCase = getCurrentUserUseCase,
       super(const AuthenticationState()) {
    
    on<AuthenticationStarted>(_onAuthenticationStarted);
    on<SignInWithApplePressed>(_onSignInWithApplePressed);
    on<SignInWithGooglePressed>(_onSignInWithGooglePressed);
    on<SignOutPressed>(_onSignOutPressed);
    on<AuthenticationStatusChanged>(_onAuthenticationStatusChanged);
  }

  Future<void> _onAuthenticationStarted(
    AuthenticationStarted event,
    Emitter<AuthenticationState> emit,
  ) async {
    try {
      final user = await _getCurrentUserUseCase();
      if (user != null) {
        emit(state.copyWith(
          status: AuthenticationStatus.authenticated,
          user: user,
        ));
      } else {
        emit(state.copyWith(
          status: AuthenticationStatus.unauthenticated,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        status: AuthenticationStatus.unauthenticated,
      ));
    }
  }

  Future<void> _onSignInWithApplePressed(
    SignInWithApplePressed event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(state.copyWith(
      status: AuthenticationStatus.loading,
      errorMessage: null,
    ));

    try {
      final user = await _signInWithAppleUseCase();
      emit(state.copyWith(
        status: AuthenticationStatus.authenticated,
        user: user,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: AuthenticationStatus.error,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onSignInWithGooglePressed(
    SignInWithGooglePressed event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(state.copyWith(
      status: AuthenticationStatus.loading,
      errorMessage: null,
    ));

    try {
      final user = await _signInWithGoogleUseCase();
      emit(state.copyWith(
        status: AuthenticationStatus.authenticated,
        user: user,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: AuthenticationStatus.error,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onSignOutPressed(
    SignOutPressed event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(state.copyWith(
      status: AuthenticationStatus.loading,
    ));

    try {
      await _signOutUseCase();
      emit(state.copyWith(
        status: AuthenticationStatus.unauthenticated,
        user: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: AuthenticationStatus.error,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onAuthenticationStatusChanged(
    AuthenticationStatusChanged event,
    Emitter<AuthenticationState> emit,
  ) async {
    if (event.user != null) {
      emit(state.copyWith(
        status: AuthenticationStatus.authenticated,
        user: event.user,
      ));
    } else {
      emit(state.copyWith(
        status: AuthenticationStatus.unauthenticated,
        user: null,
      ));
    }
  }
}

