import 'package:cal/core/network/exceptions.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/features/quick_actions/scan_food/data/datasources/scan_food_remote_datasource.dart';
import 'package:cal/features/quick_actions/scan_food/domain/repositories/scan_food_repository.dart';
import 'package:cal/features/quick_actions/scan_food/domain/usecases/recognize_food_usecase.dart';
import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

@LazySingleton(as: ScanFoodRepository)
class ScanFoodRepositoryImpl implements ScanFoodRepository {
  final ScanFoodRemoteDataSource remoteDataSource;

  ScanFoodRepositoryImpl(this.remoteDataSource);

  @override
  Future<Either<Failure, FoodModel>> recognizeFood({required AnalyzeFoodParams analyzeFoodParams}) {
    return remoteDataSource.recognizeFood(analyzeFoodParams: analyzeFoodParams);
  }
}
