import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/size_extension.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:flutter/material.dart';

import 'app_image.dart';

class MetricCard extends StatelessWidget {
  final String title;
  final String description;
  final String? icon;
  final Color color;
  final double progress;
  final VoidCallback? onTap;
  final Duration animationDuration;
  final bool horizontal;
  final double strokedWidth;

  final double progressHeight;
  final double progressWidth;

  const MetricCard({
    super.key,
    required this.title,
    required this.description,
    this.icon,
    this.horizontal = true,
    required this.color,
    this.strokedWidth = 7,
    required this.progress,
    this.onTap,
    this.animationDuration = const Duration(milliseconds: 800),
    required this.progressHeight,
    required this.progressWidth,
  });

  @override
  Widget build(BuildContext context) {
    return AppGestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: context.colorScheme.onPrimary,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: context.onSecondary.withAlpha(51),
              offset: const Offset(-2, 4),
              blurRadius: 10,
            ),
          ],
        ),
        padding: horizontal
            ? const EdgeInsetsDirectional.symmetric(horizontal: 30, vertical: 16)
            : const EdgeInsetsDirectional.symmetric(horizontal: 12, vertical: 20),
        child: horizontal
            ? Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: children(context),
              )
            : Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: children(context),
              ),
      ),
    );
  }

  List<Widget> children(BuildContext context) => [
        SizedBox(
          width: horizontal ? null : context.screenWidth,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (!horizontal) const SizedBox(height: 1),
              AnimatedFlipCounter(
                padding: EdgeInsets.zero,
                duration: const Duration(milliseconds: 1000),
                value: int.parse(title),
                curve: Curves.easeInOutCubic,
                textStyle: context.textTheme.bodyLarge!.copyWith(
                  fontWeight: FontWeight.w800,
                  color: context.primaryColor,
                  fontSize: 38,
                  letterSpacing: 0,
                  height: 1,
                ),
              ),
              Text(
                description,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: context.textTheme.bodyLarge!.copyWith(
                  fontWeight: FontWeight.w800,
                  color: context.onSecondary.withAlpha(135),
                  fontSize: 12,
                  height: 1,
                ),
              ),
              if (!horizontal) const SizedBox(height: 20),
            ],
          ),
        ),
        Stack(
          alignment: Alignment.center,
          children: [
            TweenAnimationBuilder<double>(
              duration: animationDuration,
              curve: Curves.easeInOut,
              tween: Tween<double>(
                begin: 0,
                end: progress,
              ),
              builder: (context, value, _) {
                return SizedBox(
                  width: progressWidth,
                  height: progressHeight,
                  child: CircularProgressIndicator(
                    value: value,
                    strokeWidth: strokedWidth,
                    backgroundColor: context.onPrimaryContainer.withAlpha(210),
                    valueColor: AlwaysStoppedAnimation<Color>(color),
                  ),
                );
              },
            ),
            CircleAvatar(
              radius: 17,
              backgroundColor: context.onPrimaryContainer.withAlpha(210),
              child: icon == null ? const SizedBox.shrink() : AppImage.asset(icon!),
            ),
          ],
        ),
      ];
}

double calculateSafeProgress(double? totalCalories, double? consumedCalories) {
  if (totalCalories == null || consumedCalories == null || totalCalories <= 0) {
    return 0.0;
  }

  final raw = consumedCalories / totalCalories;

  if (raw.isNaN || raw.isInfinite) {
    return 0.0;
  }

  return raw.clamp(0.0, 1.0);
}
