import 'package:cal/features/quick_actions/exercise/data/models/exercise_model.dart';
import 'package:cal/features/quick_actions/exercise/data/models/exercise_save_ai_model.dart';
import 'package:cal/features/quick_actions/exercise/domain/repositories/exercise_repository.dart';
import 'package:dartz/dartz.dart';

class SaveExerciseAiUseCase {
  final ExerciseRepository repository;

  SaveExerciseAiUseCase(this.repository);

  Future<Either<String, ExerciseModel>> call(ExerciseSaveAiModel exercise) {
    return repository.saveExerciseAi(exercise);
  }
}
