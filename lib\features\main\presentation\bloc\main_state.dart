part of 'main_bloc.dart';

enum BlocStatus { initial, loading, success, error }

class MainState {
  List<StreakModel>? streaks;
  BlocStatus status;

  MainState({this.streaks, this.status = BlocStatus.initial});

  MainState copyWith({
    List<StreakModel>? streaks,
    BlocStatus? status,
  }) {
    return MainState(
      streaks: streaks ?? this.streaks,
      status: status ?? this.status,
    );
  }
}
