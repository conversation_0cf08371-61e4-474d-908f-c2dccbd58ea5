import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/generated/assets.dart';
import 'package:flutter/material.dart';

AppBar homeAppBar(BuildContext context) {
  return AppBar(
    forceMaterialTransparency: true,
    elevation: 0,
    automaticallyImplyLeading: false,
    title: const AppImage.asset(Assets.imagesOrangeAi, height: 24, width: 24),
    // actions: [IconButton(icon: const Icon(Icons.settings), onPressed: () => context.push(const SettingsScreen()))],
  );
}
