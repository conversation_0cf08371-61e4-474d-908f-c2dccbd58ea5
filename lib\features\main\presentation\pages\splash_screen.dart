import 'dart:developer';

import 'package:cal/common/consts/app_keys.dart';
import 'package:cal/common/utils/shared_preferences_helper.dart';
import 'package:cal/features/main/presentation/pages/main_screen.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_wrapper.dart';
import 'package:cal/features/onboarding/presentation/pages/welcome_screen.dart';
import 'package:cal/features/subscriptions/presentation/pages/payment_flow.dart';
import 'package:flutter/widgets.dart';

import '../../../authentication/presentation/pages/authentication_page.dart';

Widget splashScrenn() {
  final shouldShowWelcomeScreen = ShPH.getData(key: AppKeys.shouldShowWelcomeScreen);
  final hasOnboarded = ShPH.getData(key: AppKeys.hasOnboarded);
  final paymentDone = ShPH.getData(key: AppKeys.hasCompletedPayment);
  final isAuthenticated = ShPH.getData(key: AppKeys.isAuthenticated);

  final showWelcome = shouldShowWelcomeScreen == null || shouldShowWelcomeScreen == true;
  final onboarded = hasOnboarded == true || hasOnboarded == 'true';
  final paid = paymentDone == true || paymentDone == 'true';
  final authenticated = isAuthenticated == true || isAuthenticated == 'true';

  log('SplashScreen Navigation Check:');
  log('shouldShowWelcomeScreen: $shouldShowWelcomeScreen, showWelcome: $showWelcome');
  log('hasOnboarded: $hasOnboarded, onboarded: $onboarded');
  log('paymentDone: $paymentDone, paid: $paid');
  log('isAuthenticated: $isAuthenticated, authenticated: $authenticated');

  if (showWelcome) {
    log('Navigating to WelcomeScreen');
    return const WelcomeScreen();
  }

  if (!onboarded) {
    log('Navigating to OnboardingWrapper');
    return const OnboardingWrapper();
  }

  if (!paid) {
    log('Navigating to PaymentFlow');
    return const PaymentFlow();
  }

  if (!authenticated) {
    log('Navigating to AuthenticationPage');
    return const AuthenticationPage();
  }

  log('Navigating to MainScreen');
  return const MainScreen();
}
