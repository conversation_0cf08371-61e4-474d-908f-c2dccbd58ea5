part of 'scan_food_bloc.dart';

abstract class ScanFoodEvent extends Equatable {
  const ScanFoodEvent();

  @override
  List<Object?> get props => [];
}

// Food Recognition Events
class ProcessCapturedImageEvent extends ScanFoodEvent {
  final File imageFile;

  const ProcessCapturedImageEvent({required this.imageFile});

  @override
  List<Object?> get props => [imageFile];
}

class PickImageFromGalleryEvent extends ScanFoodEvent {
  final BuildContext? context;
  const PickImageFromGalleryEvent({this.context});
}

class RecognizeFoodEvent extends ScanFoodEvent {
  final File image;

  const RecognizeFoodEvent({required this.image});

  @override
  List<Object?> get props => [image];
}

class RetryRecognizeFoodEvent extends ScanFoodEvent {
  const RetryRecognizeFoodEvent();

  @override
  List<Object?> get props => [];
}
