import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/features/quick_actions/exercise/di/exercise_injection.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'run_exercise_screen.dart';
import 'weight_lifting_exercise_screen.dart';
import 'describe_exercise_screen.dart';
import 'manual_exercise_screen.dart';

class LogExerciseScreen extends StatefulWidget {
  const LogExerciseScreen({super.key});

  @override
  State<LogExerciseScreen> createState() => _LogExerciseScreenState();
}

class _LogExerciseScreenState extends State<LogExerciseScreen> {
  final List<_ExerciseOption> _options = [
    _ExerciseOption(
      title: LocaleKeys.exercise_run_title,
      subtitle: LocaleKeys.exercise_run_subtitle,
      icon: Assets.exercisesRun,
      screen: const RunExerciseScreen(),
    ),
    _ExerciseOption(
      title: LocaleKeys.exercise_weight_lifting_title,
      subtitle: LocaleKeys.exercise_run_subtitle,
      icon: Assets.exercisesWeightLift,
      screen: const WeightLiftingExerciseScreen(),
    ),
    _ExerciseOption(
      title: LocaleKeys.exercise_describe_exercise_title,
      subtitle: LocaleKeys.exercise_describe_exercise_subtitle,
      icon: Assets.exercisesDescripe,
      screen: const DescribeExerciseScreen(),
    ),
    _ExerciseOption(
      title: LocaleKeys.exercise_manual_entry_title,
      subtitle: LocaleKeys.exercise_manual_entry_subtitle,
      icon: Assets.exercisesManual,
      screen: const ManualExerciseScreen(),
    ),
  ];

  @override
  void initState() {
    super.initState();

    initExercise();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: AppText.titleLarge(
          LocaleKeys.exercise_exercises.tr(),
          color: context.onSecondary,
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppText.titleLarge(
              LocaleKeys.exercise_add_exercise.tr(),
              color: context.onSecondary,
              fontWeight: FontWeight.bold,
            ),
            const SizedBox(height: 20),
            ...List.generate(_options.length, (index) {
              final opt = _options[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: GestureDetector(
                  onTap: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(builder: (_) => opt.screen),
                    );
                  },
                  child: Container(
                    padding: const EdgeInsets.all(16.0),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12.0),
                      border: Border.all(
                        color: context.primaryColor.withAlpha(102),
                        width: 2.0,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        AppImage.asset(opt.icon),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AppText.titleMedium(
                                opt.title.tr(),
                                color: context.onSecondary,
                                fontWeight: FontWeight.bold,
                              ),
                              AppText.bodySmall(
                                opt.subtitle.tr(),
                                color: context.onSecondary,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }),
            const Spacer(),
          ],
        ),
      ),
    );
  }
}

class _ExerciseOption {
  final String title;
  final String subtitle;
  final String icon;
  final Widget screen;

  _ExerciseOption({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.screen,
  });
}
