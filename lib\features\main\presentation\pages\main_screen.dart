import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:cal/features/home/<USER>/bloc/recent_food_bloc.dart';
import 'package:cal/features/home/<USER>/pages/home_screen.dart';
import 'package:cal/features/main/presentation/widgets/quick_action.dart';
import 'package:cal/features/quick_actions/scan_food/presentation/bloc/scan_food_bloc.dart';
import 'package:cal/features/settings/presentation/pages/settings_screen.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cal/core/di/injection.dart' as di;

import '../../../progress/presentation/pages/progress_screen.dart';
import '../bloc/main_bloc.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _selectedIndex = 0;

  final List<Widget> _pages = [
    const HomeScreen(),
    const SettingsScreen(),
    const ProgressScreen(),
  ];

  bool _isMenuOpen = false;

  late final RecentFoodBloc recentBloc;
  late final ScanFoodBloc scanBloc;

  @override
  initState() {
    super.initState();

    setState(() {
      _isMenuOpen = false;
    });
  }

  DateTime getLastSaturday() {
    DateTime now = DateTime.now();
    int daysSinceSaturday = (now.weekday + 1) % 7;
    return now.subtract(Duration(days: daysSinceSaturday));
  }

  @override
  Widget build(BuildContext context) {
    const buttonSize = 55.0;

    return MultiBlocProvider(
      providers: [
        BlocProvider<RecentFoodBloc>(
          lazy: false,
          create: (_) {
            recentBloc = di.getIt<RecentFoodBloc>()..add(LoadFood(date: DateTime.now()));
            return recentBloc;
          },
        ),
        BlocProvider<MainBloc>(
          create: (_) => di.getIt<MainBloc>()..add(GetStreakEvent(locale: context.locale)),
        ),
        BlocProvider<ScanFoodBloc>(
          lazy: false,
          create: (_) {
            scanBloc = di.getIt<ScanFoodBloc>(param1: recentBloc);
            return scanBloc;
          },
        ),
      ],
      child: Scaffold(
        backgroundColor: context.surface,
        extendBody: true,
        floatingActionButtonLocation: FloatingActionButtonLocation.endDocked,
        floatingActionButton: Builder(
          builder: (context) {
            return AppGestureDetector(
              onTap: () {
                bool isLoading = context.read<ScanFoodBloc>().state.status == ScanFoodStatus.processing;
                if (isLoading) {
                  return;
                } else {
                  setState(() {
                    _isMenuOpen = !_isMenuOpen;
                  });
                }
              },
              child: Container(
                width: buttonSize,
                height: buttonSize,
                decoration: BoxDecoration(
                  color: context.primaryColor,
                  shape: BoxShape.circle,
                  boxShadow: const [
                    BoxShadow(
                      color: Colors.black26,
                      blurRadius: 6,
                      offset: Offset(0, 3),
                    ),
                  ],
                ),
                child: Center(
                  child: Icon(_isMenuOpen ? Icons.close : Icons.add, color: Colors.white, size: 33),
                ),
              ),
            );
          },
        ),
        body: Stack(
          children: [
            _pages[_selectedIndex],
            AnimatedOpacity(
              opacity: _isMenuOpen ? 0.5 : 0,
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeInOut,
              child: IgnorePointer(
                ignoring: !_isMenuOpen,
                child: AppGestureDetector(
                  onTap: () => setState(() {
                    _isMenuOpen = false;
                  }),
                  child: Container(
                    color: Colors.black,
                    width: double.infinity,
                    height: double.infinity,
                  ),
                ),
              ),
            ),
            if (_isMenuOpen)
              Positioned(
                bottom: 120,
                right: 16,
                left: 16,
                child: QuickActionsGrid(
                  isVisible: _isMenuOpen,
                  onActionSelected: () {
                    setState(() {
                      _isMenuOpen = false;
                    });
                  },
                ),
              ),
          ],
        ),
        bottomNavigationBar: BottomAppBar(
          shape: const CircularNotchedRectangle(),
          notchMargin: 16,
          padding: const EdgeInsets.only(bottom: 12, top: 6),
          height: 75,
          color: Colors.white,
          shadowColor: Colors.black,
          elevation: 12,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(child: _buildNavItem(icon: Icons.home_outlined, index: 0)),
              Expanded(child: _buildNavItem(icon: Icons.bar_chart_outlined, index: 2)),
              Expanded(child: _buildNavItem(icon: Icons.settings_outlined, index: 1)),
              const Expanded(child: SizedBox()),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem({required IconData icon, required int index}) {
    final isSelected = _selectedIndex == index;
    return AppGestureDetector(
      onTap: () => setState(() => _selectedIndex = index),
      child: Padding(
        padding: const EdgeInsets.only(bottom: 4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 30,
              color: isSelected ? context.onSecondaryContainer : context.onSecondaryContainer.withAlpha(200),
            ),
            const SizedBox(height: 1),
            Text(
              [
                LocaleKeys.navbar_home.tr(),
                LocaleKeys.navbar_settings.tr(),
                LocaleKeys.navbar_progress.tr(),
              ][index],
              style: context.textTheme.labelMedium!.copyWith(
                color: isSelected ? context.onSecondaryContainer : context.onSecondaryContainer.withAlpha(200),
                fontWeight: isSelected ? FontWeight.w900 : FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
